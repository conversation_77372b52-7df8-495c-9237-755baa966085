{"name": "mediscan-ai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start-chroma": "node start-chroma-server.js"}, "dependencies": {"@google/generative-ai": "^0.2.1", "chromadb": "^2.4.3", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.22.1", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}